'use client';

import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount, useDisconnect } from 'wagmi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Modal } from '@/components/shared/modal';
import {
  WalletIcon,
  ExternalLinkIcon,
  CopyIcon,
  CheckIcon,
  LogOutIcon,
  ShieldCheckIcon,
  AlertCircleIcon
} from 'lucide-react';
import { useState, useEffect } from 'react';

interface WalletConnectProps {
  showDisconnect?: boolean;
  className?: string;
  onConnect?: (address: string) => void;
  onDisconnect?: () => void;
}

export function WalletConnect({
  showDisconnect = true,
  className = '',
  onConnect,
  onDisconnect
}: WalletConnectProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();

  const handleDisconnect = async () => {
    disconnect();
    if (onDisconnect) {
      onDisconnect();
    }
  };

  const copyAddress = async () => {
    if (address) {
      await navigator.clipboard.writeText(address);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  // If connected, show wallet info
  if (isConnected && address) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
            Connected
          </Badge>
          <span className="text-sm font-mono">{formatAddress(address)}</span>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={copyAddress}
            className="flex items-center gap-1"
          >
            {copied ? <CheckIcon className="h-3 w-3" /> : <CopyIcon className="h-3 w-3" />}
            {copied ? 'Copied!' : 'Copy'}
          </Button>
          
          {showDisconnect && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleDisconnect}
              className="flex items-center gap-1 text-red-600 hover:text-red-700"
            >
              <LogOutIcon className="h-3 w-3" />
              Disconnect
            </Button>
          )}
        </div>
      </div>
    );
  }

  // If not connected, show connect button
  return (
    <div className={className}>
      <ConnectButton.Custom>
        {({
          account,
          chain,
          openAccountModal,
          openChainModal,
          openConnectModal,
          authenticationStatus,
          mounted,
        }) => {
          // Note: If your app doesn't use authentication, you
          // can remove all 'authenticationStatus' checks
          const ready = mounted && authenticationStatus !== 'loading';
          const connected =
            ready &&
            account &&
            chain &&
            (!authenticationStatus ||
              authenticationStatus === 'authenticated');

          return (
            <div
              {...(!ready && {
                'aria-hidden': true,
                'style': {
                  opacity: 0,
                  pointerEvents: 'none',
                  userSelect: 'none',
                },
              })}
            >
              {(() => {
                if (!connected) {
                  return (
                    <Button
                      onClick={openConnectModal}
                      className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:from-[#4A148C]/90 hover:to-[#7B1FA2]/90 transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      <WalletIcon className="h-4 w-4 mr-2" />
                      Connect Wallet
                    </Button>
                  );
                }

                if (chain.unsupported) {
                  return (
                    <Button
                      onClick={openChainModal}
                      variant="destructive"
                    >
                      Wrong network
                    </Button>
                  );
                }

                return (
                  <div className="flex gap-2">
                    <Button
                      onClick={openChainModal}
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      {chain.hasIcon && (
                        <div
                          style={{
                            background: chain.iconBackground,
                            width: 12,
                            height: 12,
                            borderRadius: 999,
                            overflow: 'hidden',
                            marginRight: 4,
                          }}
                        >
                          {chain.iconUrl && (
                            <img
                              alt={chain.name ?? 'Chain icon'}
                              src={chain.iconUrl}
                              style={{ width: 12, height: 12 }}
                            />
                          )}
                        </div>
                      )}
                      {chain.name}
                    </Button>

                    <Button
                      onClick={openAccountModal}
                      variant="outline"
                    >
                      {account.displayName}
                      {account.displayBalance
                        ? ` (${account.displayBalance})`
                        : ''}
                    </Button>
                  </div>
                );
              })()}
            </div>
          );
        }}
      </ConnectButton.Custom>
    </div>
  );
}

// Hook to use wallet connection state
export function useWalletConnection() {
  const { address, isConnected, isConnecting } = useAccount();

  return {
    address,
    isConnected,
    isConnecting,
  };
}
